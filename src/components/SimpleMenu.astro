---
import { getCompleteMenu } from '../utils/menuParser.ts';

// Get the menu data - this will read menu.md and process @include: directives
const menuData = getCompleteMenu();

// Function to extract the main title from markdown content
function extractTitle(markdown: string): string {
  const match = markdown.match(/^# (.+)$/m);
  return match ? match[1] : 'Untitled Product';
}

// Function to create a URL-friendly slug from a title
function createSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}

// Function to convert markdown to HTML with ID support
function markdownToHtml(markdown: string, productId?: string): string {
  let html = markdown
    // Headers with ID for main title
    .replace(/^# (.*$)/gm, (_, title) => {
      if (productId) {
        return `<h1 id="${productId}">${title}</h1>`;
      }
      return `<h1>${title}</h1>`;
    })
    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
    // Bold
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // Links
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
    // Lists
    .replace(/^- (.*$)/gm, '<li>$1</li>')
    .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
    // Paragraphs
    .replace(/^(?!<[h|u|l])(.*$)/gm, '<p>$1</p>')
    // Clean up empty paragraphs
    .replace(/<p><\/p>/g, '')
    .replace(/<p>\s*<\/p>/g, '');

  return html;
}

// Generate TOC data from all products in all categories
const allProducts = menuData.sections
  .filter(section => section.type === 'category')
  .flatMap(section => section.products || []);

const tocItems = allProducts.map((product) => {
  const title = extractTitle(product.content);
  const slug = createSlug(title);
  const section = product.filename?.includes('shopinfo/') ? 'Shop Information' :
                 product.filename?.includes('products/') ? 'Products' : 'Other';
  return {
    title,
    slug,
    filename: product.filename,
    section,
    category: product.category,
    categoryIndex: product.categoryIndex
  };
});

// Group TOC items by section
const tocSections = tocItems.reduce((acc, item) => {
  if (!acc[item.section]) {
    acc[item.section] = [];
  }
  acc[item.section].push(item);
  return acc;
}, {} as Record<string, typeof tocItems>);
---

<div class="menu-container">
  <!-- Render content sections first (main heading and intro text) -->
  <div class="menu-content">
    {menuData.sections.map((section) => (
      section.type === 'content' && (
        <div class="content-section">
          <div set:html={markdownToHtml(section.content || '')}></div>
        </div>
      )
    ))}
  </div>

  <!-- Table of Contents (after intro, before categories) -->
  {tocItems.length > 0 && (
    <div class="toc-section">
      <h2>📋 Table of Contents</h2>
      <nav class="toc-nav">
        {Object.entries(tocSections).map(([sectionName, items]) => (
          <div class="toc-section-group">
            <h3 class="toc-section-title">{sectionName}</h3>
            <ul class="toc-list">
              {items.map((item) => (
                <li class="toc-item">
                  <a href={`#${item.slug}`} class="toc-link">
                    {item.title}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </nav>
    </div>
  )}

  <!-- Render category sections -->
  <div class="categories-content">
    {menuData.sections.map((section) => (
      section.type === 'category' && (
        <div class="category-section" data-category-index={section.categoryIndex}>
          <h2 class="category-header">{section.categoryName}</h2>
          <div class="category-products">
            {section.products?.map((product, productIndex) => {
              const productSlug = createSlug(extractTitle(product.content));
              const productNumber = `${section.categoryIndex}.${productIndex + 1}`;
              return (
                <div class="product-item" data-product-number={productNumber}>
                  <div set:html={markdownToHtml(product.content, productSlug)}></div>
                </div>
              );
            })}
          </div>
        </div>
      )
    ))}
  </div>
</div>

<style>
  .menu-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    font-family: system-ui, sans-serif;
  }

  .menu-content {
    margin-bottom: 2rem;
  }

  .categories-content {
    margin-top: 1rem;
  }

  .content-section {
    margin-bottom: 2rem;
  }

  .content-section h1 {
    color: #2c3e50;
    border-bottom: 3px solid #3498db;
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
  }

  /* Category sections */
  .category-section {
    margin-bottom: 3rem;
  }

  .category-header {
    color: #2c3e50;
    font-size: 1.8rem;
    margin-bottom: 2rem;
    padding-bottom: 0.75rem;
    border-bottom: 3px solid #3498db;
  }

  .category-products {
    display: grid;
    gap: 2rem;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  }

  /* Table of Contents Styles */
  .toc-section {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 3rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .toc-section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.4rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
  }

  .toc-nav {
    max-width: 100%;
  }

  .toc-section-group {
    margin-bottom: 2rem;
  }

  .toc-section-group:last-child {
    margin-bottom: 0;
  }

  .toc-section-title {
    color: #34495e;
    font-size: 1.1rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
  }

  .toc-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }

  .toc-item {
    margin: 0;
  }

  .toc-link {
    display: block;
    padding: 1rem 1.5rem;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    color: #2c3e50;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .toc-link:hover {
    background: #3498db;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    border-color: #3498db;
  }

  .toc-link:active {
    transform: translateY(0);
  }

  /* Category sections are now handled above */

  .product-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .product-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  .product-item h1 {
    color: #2c3e50;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
    position: relative;
  }

  /* Product numbering is handled by JavaScript */

  .product-item h2 {
    color: #34495e;
    font-size: 1.2rem;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }

  .product-item ul {
    margin: 1rem 0;
    padding-left: 1.5rem;
  }

  .product-item li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
  }

  .product-item strong {
    color: #2c3e50;
  }

  .product-item a {
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
  }

  .product-item a:hover {
    text-decoration: underline;
    color: #2980b9;
  }

  .product-item p {
    line-height: 1.6;
    margin-bottom: 1rem;
    color: #555;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Add scroll margin to account for any fixed headers */
  .product-item h1[id] {
    scroll-margin-top: 2rem;
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .menu-container {
      padding: 1rem;
    }

    .products-section {
      grid-template-columns: 1fr;
    }

    .product-item {
      padding: 1.5rem;
    }

    .toc-section {
      padding: 1.5rem;
      margin-bottom: 2rem;
    }

    .toc-list {
      grid-template-columns: 1fr;
      gap: 0.75rem;
    }

    .toc-link {
      padding: 0.75rem 1rem;
      font-size: 0.9rem;
    }
  }
</style>

<script>
  // Add category and product numbering
  document.addEventListener('DOMContentLoaded', function() {
    // Add category numbering to category headers
    const categoryHeaders = document.querySelectorAll('.category-header');
    categoryHeaders.forEach((header, index) => {
      const categoryNumber = index + 1;
      header.textContent = `${categoryNumber}. ${header.textContent}`;
    });

    // Add product numbering to product items
    const productItems = document.querySelectorAll('.product-item[data-product-number]');
    productItems.forEach((productItem) => {
      const productNumber = productItem.getAttribute('data-product-number');
      const firstH1 = productItem.querySelector('h1');
      if (firstH1 && productNumber) {
        firstH1.textContent = `${productNumber} ${firstH1.textContent}`;
      }
    });
  });
</script>
